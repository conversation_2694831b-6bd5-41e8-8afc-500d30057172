import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { MessageSquare, Loader2 } from 'lucide-react';
import { useWebSocketCreateConversation } from '@/hooks/useWebSocketData';
import { useToast } from '@/hooks/use-toast';

interface MessageButtonProps {
  nurseId: string;
  nurseName: string;
  variant?:
    | 'default'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
    | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  fullWidth?: boolean;
  disabled?: boolean;
}

const MessageButton: React.FC<MessageButtonProps> = ({
  nurseId,
  nurseName,
  variant = 'default',
  size = 'default',
  className = '',
  fullWidth = false,
  disabled = false,
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const createConversationMutation = useWebSocketCreateConversation();
  const isLoading = createConversationMutation.isLoading;
  const [isNavigating, setIsNavigating] = useState(false);

  const isButtonLoading = isLoading || isNavigating;

  const handleClick = async () => {
    if (isButtonLoading || disabled) return;

    try {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast({
          title: 'Authentication Required',
          description: 'Please log in to chat with a nurse',
          variant: 'destructive',
        });
        navigate('/login', { state: { returnTo: window.location.pathname } });
        return;
      }

      setIsNavigating(true);
      const response = await createConversationMutation.mutate({
        nurseId,
        nurseName,
      });

      if (response.success && response.data?.conversation) {
        navigate(`/chat/${response.data.conversation.id}`, {
          state: { nurseName, nurseId },
        });
      } else {
        throw new Error(response.message || 'Failed to create conversation');
      }
    } catch (error) {
      console.error('Error starting conversation:', error);
      toast({
        title: 'Chat Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to start conversation',
        variant: 'destructive',
      });
      setIsNavigating(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={isButtonLoading || disabled}
      className={`${fullWidth ? 'w-full' : ''} ${className}`}
      aria-label={`Message ${nurseName}`}
    >
      {isButtonLoading ? (
        <Loader2 className='h-4 w-4 mr-2 animate-spin' />
      ) : (
        <MessageSquare className='h-4 w-4 mr-2' />
      )}
      Message
    </Button>
  );
};

export default MessageButton;
