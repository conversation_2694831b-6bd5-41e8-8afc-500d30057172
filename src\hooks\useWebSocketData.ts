import { useState, useEffect, useCallback, useRef } from 'react';
import useWebSocket from './useWebSocket';
import type {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  SearchMessagesResponse,
  CreateConversationRequest,
  GetMessagesParams,
  GetConversationsParams,
  SearchMessagesParams,
} from '@/store/api/chatApiSlice';

// Data state interfaces
export interface DataState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

export interface UseWebSocketDataOptions {
  enabled?: boolean;
  cacheTime?: number; // Cache duration in ms
  staleTime?: number; // Time before data is considered stale
  refetchOnWindowFocus?: boolean;
  refetchInterval?: number;
}

// Cache management
class DataCache {
  private cache = new Map<string, CacheEntry<unknown>>();
  private defaultCacheTime = 5 * 60 * 1000; // 5 minutes
  private defaultStaleTime = 30 * 1000; // 30 seconds

  set<T>(key: string, data: T, cacheTime?: number): void {
    const now = Date.now();
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + (cacheTime || this.defaultCacheTime),
    };
    this.cache.set(key, entry);
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    if (!entry) return null;
    
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  isStale(key: string, staleTime?: number): boolean {
    const entry = this.cache.get(key);
    if (!entry) return true;
    
    const staleThreshold = staleTime || this.defaultStaleTime;
    return Date.now() - entry.timestamp > staleThreshold;
  }

  invalidate(key: string): void {
    this.cache.delete(key);
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }
}

// Global cache instance
const globalCache = new DataCache();

// Hook for conversations list
export function useWebSocketConversations(
  params?: GetConversationsParams,
  options: UseWebSocketDataOptions = {}
) {
  const {
    enabled = true,
    cacheTime = 5 * 60 * 1000,
    staleTime = 30 * 1000,
    refetchOnWindowFocus = true,
    refetchInterval,
  } = options;

  const cacheKey = `conversations_${JSON.stringify(params || {})}`;
  const [state, setState] = useState<DataState<ConversationsResponse>>({
    data: globalCache.get<ConversationsResponse>(cacheKey),
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !wsHook.status.connected) return;

    // Check if data is fresh enough
    if (state.data && !globalCache.isStale(cacheKey, staleTime)) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await wsHook.requestConversations(params);
      const now = Date.now();
      
      globalCache.set(cacheKey, response, cacheTime);
      setState({
        data: response,
        isLoading: false,
        error: null,
        lastFetched: now,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch conversations',
      }));
    }
  }, [enabled, wsHook.status.connected, wsHook.requestConversations, params, cacheKey, staleTime, cacheTime, state.data]);

  const refetch = useCallback(() => {
    globalCache.invalidate(cacheKey);
    return fetchData();
  }, [cacheKey, fetchData]);

  // Initial fetch
  useEffect(() => {
    if (enabled && wsHook.status.connected) {
      fetchData();
    }
  }, [enabled, wsHook.status.connected, fetchData]);

  // Refetch interval
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [refetchInterval, enabled, fetchData]);

  // Refetch on window focus
  useEffect(() => {
    if (!refetchOnWindowFocus || !enabled) return;

    const handleFocus = () => {
      if (globalCache.isStale(cacheKey, staleTime)) {
        fetchData();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, enabled, cacheKey, staleTime, fetchData]);

  return {
    ...state,
    refetch,
    isConnected: wsHook.status.connected,
  };
}

// Hook for single conversation
export function useWebSocketConversation(
  conversationId: string | null,
  options: UseWebSocketDataOptions = {}
) {
  const {
    enabled = true,
    cacheTime = 5 * 60 * 1000,
    staleTime = 30 * 1000,
    refetchOnWindowFocus = true,
  } = options;

  const cacheKey = `conversation_${conversationId}`;
  const [state, setState] = useState<DataState<ConversationResponse>>({
    data: globalCache.get<ConversationResponse>(cacheKey),
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !wsHook.status.connected || !conversationId) return;

    // Check if data is fresh enough
    if (state.data && !globalCache.isStale(cacheKey, staleTime)) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await wsHook.requestConversation(conversationId);
      const now = Date.now();
      
      globalCache.set(cacheKey, response, cacheTime);
      setState({
        data: response,
        isLoading: false,
        error: null,
        lastFetched: now,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch conversation',
      }));
    }
  }, [enabled, wsHook.status.connected, wsHook.requestConversation, conversationId, cacheKey, staleTime, cacheTime, state.data]);

  const refetch = useCallback(() => {
    globalCache.invalidate(cacheKey);
    return fetchData();
  }, [cacheKey, fetchData]);

  // Initial fetch
  useEffect(() => {
    if (enabled && wsHook.status.connected && conversationId) {
      fetchData();
    }
  }, [enabled, wsHook.status.connected, conversationId, fetchData]);

  // Refetch on window focus
  useEffect(() => {
    if (!refetchOnWindowFocus || !enabled || !conversationId) return;

    const handleFocus = () => {
      if (globalCache.isStale(cacheKey, staleTime)) {
        fetchData();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, enabled, conversationId, cacheKey, staleTime, fetchData]);

  return {
    ...state,
    refetch,
    isConnected: wsHook.status.connected,
  };
}

// Hook for messages
export function useWebSocketMessages(
  params: GetMessagesParams | null,
  options: UseWebSocketDataOptions = {}
) {
  const {
    enabled = true,
    cacheTime = 5 * 60 * 1000,
    staleTime = 10 * 1000, // Messages are more dynamic, shorter stale time
    refetchOnWindowFocus = true,
  } = options;

  const cacheKey = `messages_${JSON.stringify(params || {})}`;
  const [state, setState] = useState<DataState<MessagesResponse>>({
    data: globalCache.get<MessagesResponse>(cacheKey),
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !wsHook.status.connected || !params) return;

    // Check if data is fresh enough
    if (state.data && !globalCache.isStale(cacheKey, staleTime)) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await wsHook.requestMessages(params);
      const now = Date.now();

      globalCache.set(cacheKey, response, cacheTime);
      setState({
        data: response,
        isLoading: false,
        error: null,
        lastFetched: now,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch messages',
      }));
    }
  }, [enabled, wsHook.status.connected, wsHook.requestMessages, params, cacheKey, staleTime, cacheTime, state.data]);

  const refetch = useCallback(() => {
    globalCache.invalidate(cacheKey);
    return fetchData();
  }, [cacheKey, fetchData]);

  // Initial fetch
  useEffect(() => {
    if (enabled && wsHook.status.connected && params) {
      fetchData();
    }
  }, [enabled, wsHook.status.connected, params, fetchData]);

  // Refetch on window focus
  useEffect(() => {
    if (!refetchOnWindowFocus || !enabled || !params) return;

    const handleFocus = () => {
      if (globalCache.isStale(cacheKey, staleTime)) {
        fetchData();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, enabled, params, cacheKey, staleTime, fetchData]);

  return {
    ...state,
    refetch,
    isConnected: wsHook.status.connected,
  };
}

// Mutation hooks
export interface MutationState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
}

export function useWebSocketCreateConversation() {
  const [state, setState] = useState<MutationState<ConversationResponse>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled: true,
  });

  const mutate = useCallback(async (params: CreateConversationRequest) => {
    setState({ data: null, isLoading: true, error: null });

    try {
      const response = await wsHook.requestCreateConversation(params);
      setState({ data: response, isLoading: false, error: null });

      // Invalidate conversations cache
      globalCache.invalidatePattern('conversations_');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create conversation';
      setState({ data: null, isLoading: false, error: errorMessage });
      throw error;
    }
  }, [wsHook.requestCreateConversation]);

  return {
    ...state,
    mutate,
    isConnected: wsHook.status.connected,
  };
}

export function useWebSocketSendMessage() {
  const [state, setState] = useState<MutationState<{ success: boolean }>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled: true,
  });

  const mutate = useCallback(async (conversationId: string, content: string, metadata?: Record<string, unknown>) => {
    setState({ data: null, isLoading: true, error: null });

    try {
      const success = wsHook.sendTextMessage(conversationId, content, metadata);
      if (success) {
        setState({ data: { success: true }, isLoading: false, error: null });

        // Invalidate messages and conversations cache
        globalCache.invalidatePattern(`messages_.*${conversationId}`);
        globalCache.invalidatePattern('conversations_');

        return { success: true };
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      setState({ data: null, isLoading: false, error: errorMessage });
      throw error;
    }
  }, [wsHook.sendTextMessage]);

  return {
    ...state,
    mutate,
    isConnected: wsHook.status.connected,
  };
}

export function useWebSocketMarkMessagesRead() {
  const [state, setState] = useState<MutationState<{ success: boolean }>>({
    data: null,
    isLoading: false,
    error: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled: true,
  });

  const mutate = useCallback(async (conversationId: string) => {
    setState({ data: null, isLoading: true, error: null });

    try {
      const response = await wsHook.requestMarkMessagesRead(conversationId);
      setState({ data: response, isLoading: false, error: null });

      // Invalidate messages and conversations cache
      globalCache.invalidatePattern(`messages_.*${conversationId}`);
      globalCache.invalidatePattern('conversations_');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to mark messages as read';
      setState({ data: null, isLoading: false, error: errorMessage });
      throw error;
    }
  }, [wsHook.requestMarkMessagesRead]);

  return {
    ...state,
    mutate,
    isConnected: wsHook.status.connected,
  };
}

export function useWebSocketSearchMessages() {
  const [state, setState] = useState<DataState<SearchMessagesResponse>>({
    data: null,
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled: true,
  });

  const search = useCallback(async (params: SearchMessagesParams) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await wsHook.requestSearchMessages(params);
      setState({
        data: response,
        isLoading: false,
        error: null,
        lastFetched: Date.now(),
      });
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search messages';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      throw error;
    }
  }, [wsHook.requestSearchMessages]);

  return {
    ...state,
    search,
    isConnected: wsHook.status.connected,
  };
}

// Hook for nurse info
export function useWebSocketNurseInfo(
  nurseId: string | null,
  options: UseWebSocketDataOptions = {}
) {
  const {
    enabled = true,
    cacheTime = 10 * 60 * 1000, // 10 minutes for nurse info
    staleTime = 5 * 60 * 1000, // 5 minutes
  } = options;

  const cacheKey = `nurse_${nurseId}`;
  const [state, setState] = useState<DataState<{ nurse: { id: string; name: string; profileImage?: string } }>>({
    data: globalCache.get(cacheKey),
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !wsHook.status.connected || !nurseId) return;

    if (state.data && !globalCache.isStale(cacheKey, staleTime)) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await wsHook.requestNurseInfo(nurseId);
      const now = Date.now();

      globalCache.set(cacheKey, response, cacheTime);
      setState({
        data: response,
        isLoading: false,
        error: null,
        lastFetched: now,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch nurse info',
      }));
    }
  }, [enabled, wsHook.status.connected, wsHook.requestNurseInfo, nurseId, cacheKey, staleTime, cacheTime, state.data]);

  useEffect(() => {
    if (enabled && wsHook.status.connected && nurseId) {
      fetchData();
    }
  }, [enabled, wsHook.status.connected, nurseId, fetchData]);

  return {
    ...state,
    refetch: fetchData,
    isConnected: wsHook.status.connected,
  };
}

// Hook for unread count
export function useWebSocketUnreadCount(
  options: UseWebSocketDataOptions = {}
) {
  const {
    enabled = true,
    cacheTime = 30 * 1000, // 30 seconds for unread count
    staleTime = 10 * 1000, // 10 seconds
    refetchInterval = 30 * 1000, // Refetch every 30 seconds
  } = options;

  const cacheKey = 'unread_count';
  const [state, setState] = useState<DataState<{ unreadCount: number }>>({
    data: globalCache.get(cacheKey),
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const wsHook = useWebSocket({
    url: import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com/ws',
    token: localStorage.getItem('idToken') || '',
    userId: localStorage.getItem('userId') || '',
    userType: 'patient',
    userName: localStorage.getItem('userGivenName') || 'Patient',
    enabled,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !wsHook.status.connected) return;

    if (state.data && !globalCache.isStale(cacheKey, staleTime)) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await wsHook.requestUnreadCount();
      const now = Date.now();

      globalCache.set(cacheKey, response, cacheTime);
      setState({
        data: response,
        isLoading: false,
        error: null,
        lastFetched: now,
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch unread count',
      }));
    }
  }, [enabled, wsHook.status.connected, wsHook.requestUnreadCount, cacheKey, staleTime, cacheTime, state.data]);

  const refetch = useCallback(() => {
    globalCache.invalidate(cacheKey);
    return fetchData();
  }, [cacheKey, fetchData]);

  // Initial fetch
  useEffect(() => {
    if (enabled && wsHook.status.connected) {
      fetchData();
    }
  }, [enabled, wsHook.status.connected, fetchData]);

  // Refetch interval
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [refetchInterval, enabled, fetchData]);

  return {
    ...state,
    refetch,
    isConnected: wsHook.status.connected,
  };
}

// Cache utilities for external use
export const cacheUtils = {
  invalidateConversations: () => globalCache.invalidatePattern('conversations_'),
  invalidateMessages: (conversationId?: string) => {
    if (conversationId) {
      globalCache.invalidatePattern(`messages_.*${conversationId}`);
    } else {
      globalCache.invalidatePattern('messages_');
    }
  },
  invalidateUnreadCount: () => globalCache.invalidate('unread_count'),
  clearAll: () => globalCache.clear(),
};
